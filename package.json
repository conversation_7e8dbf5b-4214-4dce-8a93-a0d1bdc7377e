{"name": "vidmob-client-session-manager", "version": "0.45.0", "description": "Provides a single package to authenticate a user with the vidmob-node-auth-api (login), manage session and refresh tokens. This also provides access to all VidMob API endpoints.", "main": "lib/index.js", "scripts": {"build": "rimraf lib && babel src --out-dir lib --ignore '**/*.spec.js'", "test": "jest", "test:watch": "jest --watchAll"}, "jest": {"verbose": true, "testEnvironment": "node", "moduleNameMapper": {"^.+\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/configs/mocks/fileMock.js", "^.+\\.(css|scss)$": "<rootDir>/configs/mocks/styleMock.js"}, "moduleDirectories": ["node_modules"], "moduleFileExtensions": ["js"]}, "author": "VidMob", "dependencies": {"AsyncStorage": "^0.1.5", "axios": "1.7.7", "babel-plugin-add-module-exports": "^0.2.1", "js-cookie": "^3.0.1", "lodash": "^4.17.11", "lodash.isfunction": "^3.0.9", "lodash.isnil": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.21.0", "@babel/core": "^7.21.3", "@babel/preset-env": "^7.20.2", "babel-jest": "^29.5.0", "eslint": "4.18.2", "eslint-config-xo": "0.20.1", "eslint-config-xo-space": "0.18.0", "jest": "^26.6.3", "rimraf": "2.6.2"}}