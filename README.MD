# vidmob-client-session-manager
The client session manager works in conjunction with the node-auth-api package to handle the users session.
When the user logs in to an application the CSM calls the Node Auth endpoint and gets a refresh token and a session id.   
The CSM then adds the information needed to the users browser storage. Each call made to our API is intercepted by the 
CSM which validates the users session and passes the request on if valid. An invalid session will result in calling the
callback method that was passed to the CSM when it was instantiated letting the client that the user needs to login again.

**How to expire a session for testing.**

When you need to test an application to make sure that it handles an expired session properly 
you can use the `clearSession()` function. Add this to your application in a timeout function.
See example below.

`
setTimeout(() => {
   vmSessionManager.clearSession();
 }, 30000);
 `

### **Make sure you're running:**
* node version >= 6
* npm version >= 3.10
* install babel-cli 6 globally [how to set babel 6](https://babeljs.io/blog/2015/10/31/setting-up-babel-6)

**Getting Started**

1- Install all dependencies

    npm install

**Run Local Tests**

    npm run test

**How to do a new Release**

1- Bump the release version in the package.json 

2- Build your new release

    npm run build

3- Follow Git instructions on how to create a release [here](https://help.github.com/articles/creating-releases/)

**How to test this LOCALLY on any VidMob platform**

1- Build the release you want to release

    npm run build

2- Pack the entire thing

    npm pack

You should get a zip file at the root level like **vidmob-client-session-manager-0.19.0.tgz** for example. It is named using
package.json "name" and "version" fields.

From the platform you want to test it at (like ACS):

2.1 Change the package.json dependencies list to 

    ...
    dependencies: {
        ...
        "vidmob-client-session-manager": "./../vidmob-client-session-manager/vidmob-client-session-manager-0.19.0.tgz"
        ...
    }

you pretty much need to path to the vidmob-client-session-manager packed zip file, in this case **vidmob-client-session-manager-0.19.0.tgz**

2.2 Run a ```npm install``` in the environment that is using vidmob-client-session-manager and check if all works 

VidMob Jan 2019
