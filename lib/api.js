"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _lodash = _interopRequireDefault(require("lodash.isnil"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
var VidMobAuthApi = /*#__PURE__*/function () {
  function VidMobAuthApi(vmApiManager, url, tokenRequired) {
    _classCallCheck(this, VidMobAuthApi);
    this.vmApiManager = vmApiManager;
    this.url = url;
    this.tokenRequired = tokenRequired || false;
  }
  _createClass(VidMobAuthApi, [{
    key: "prepareUrl",
    value: function prepareUrl(url, urlPathFills) {
      var pathsToReplace = this.url.match(/:([a-zA-Z0-9]+)/g);
      if ((0, _lodash["default"])(pathsToReplace)) {
        return url;
      }
      pathsToReplace.forEach(function (path) {
        var pattern = new RegExp(path);
        var value = urlPathFills ? urlPathFills[path.substring(1)] : null;
        url = (0, _lodash["default"])(value) ? url.replace(pattern, '') : url.replace(pattern, value.toString());
      });
      return url;
    }
  }, {
    key: "get",
    value: function get(params) {
      var _this = this;
      return this.vmApiManager.getInstance(this.tokenRequired).then(function (axios) {
        return axios.get(_this.prepareUrl(_this.url, params), {
          params: params
        });
      });
    }
  }, {
    key: "post",
    value: function post(params) {
      var _this2 = this;
      return this.vmApiManager.getInstance(this.tokenRequired).then(function (axios) {
        return axios.post(_this2.prepareUrl(_this2.url, params), params);
      });
    }
  }, {
    key: "patch",
    value: function patch(params) {
      var _this3 = this;
      return this.vmApiManager.getInstance(this.tokenRequired).then(function (axios) {
        return axios.patch(_this3.prepareUrl(_this3.url, params), params);
      });
    }
  }, {
    key: "put",
    value: function put(params) {
      var _this4 = this;
      return this.vmApiManager.getInstance(this.tokenRequired).then(function (axios) {
        return axios.put(_this4.prepareUrl(_this4.url, params), params);
      });
    }
  }, {
    key: "delete",
    value: function _delete(params) {
      var _this5 = this;
      return this.vmApiManager.getInstance(this.tokenRequired).then(function (axios) {
        return axios["delete"](_this5.prepareUrl(_this5.url, params), {
          params: params
        });
      });
    }
  }, {
    key: "getById",
    value: function getById(id) {
      return this.get({
        id: id
      });
    }
  }]);
  return VidMobAuthApi;
}();
exports["default"] = VidMobAuthApi;