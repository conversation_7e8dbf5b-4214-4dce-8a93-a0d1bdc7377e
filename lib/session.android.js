"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _axios = _interopRequireDefault(require("axios"));
var _lodash = _interopRequireDefault(require("lodash.isnil"));
var _reactNative = require("react-native");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function storageAvailable(type) {
  var storage = type;
  var x = '__storage_test__';
  try {
    storage.setItem(x, x);
    storage.removeItem(x);
    return true;
  } catch (e) {
    console.error(e);
    return e.name;
  }
}
var VidMobSessionManager = /*#__PURE__*/function () {
  function VidMobSessionManager(config) {
    _classCallCheck(this, VidMobSessionManager);
    // this.sessionApiUrl = config.authApiAndroidStudioURL; //for Android Studio emulator
    this.sessionApiUrl = config.authApiThirdPartyURL; // for non-Android Studio emulators
    this.asyncStorage = _reactNative.AsyncStorage;
    this.storageObj = _reactNative.AsyncStorage;
    this.sessionIdStorageName = 'vm_session';
    this.sessionInfo = {
      sessionId: null,
      accessToken: null,
      expiresTimestamp: null,
      roles: [],
      store: false
    };
  }
  _createClass(VidMobSessionManager, [{
    key: "sessionInStorage",
    value: function sessionInStorage(storageType) {
      if (storageType.getItem(this.sessionIdStorageName)) {
        this.sessionInfo = storageType.getItem(this.sessionIdStorageName);
        return true;
      }
      return false;
    }
  }, {
    key: "setStorageType",
    value: function setStorageType(type) {
      this.storageObj = type;
    }
  }, {
    key: "loadSessionInfo",
    value: function loadSessionInfo() {
      if (storageAvailable(this.asyncStorage)) {
        this.setStorageType(this.asyncStorage);
      }
    }
  }, {
    key: "getToken",
    value: function getToken() {
      return this.sessionInfo.accessToken;
    }
  }, {
    key: "logout",
    value: function logout() {
      if (this.storageObj !== null) {
        this.storageObj.removeItem(this.sessionIdStorageName);
      }
    }
  }, {
    key: "login",
    value: function login(userCredentials) {
      var _this = this;
      var userResponse = {};
      return (0, _axios["default"])({
        method: 'post',
        url: this.sessionApiUrl + '/api/login',
        data: {
          username: userCredentials.username,
          password: userCredentials.password
        }
      }).then(function (response) {
        userResponse.sessionId = response.data.result.sessionId;
        userResponse.accessToken = response.data.result.accessToken;
        userResponse.roles = response.data.result.roles;
        userResponse.expiresIn = response.data.result.expiresIn;
        _this.updateSessionInfo(userResponse);
        return response;
      })["catch"](function (err) {
        return console.log(err);
      });
    }
  }, {
    key: "requestNewToken",
    value: function requestNewToken() {
      return (0, _axios["default"])({
        method: 'get',
        url: this.sessionApiUrl + '/api/session/' + this.sessionInfo.sessionId
      }).then(function (response) {
        return response;
      })["catch"](function (error) {
        return error;
      });
    }
  }, {
    key: "sessionIsValid",
    value: function sessionIsValid() {
      var _this2 = this;
      return new Promise(function (resolve, reject) {
        if (_this2.sessionInfo.sessionId === null) {
          reject(new Error('No valid session'));
        } else {
          var now = new Date().getTime();
          if (_this2.sessionInfo.expiresTimestamp < now) {
            return _this2.requestNewToken().then(function (response) {
              _this2.updateSessionInfo(response.data.result);
              return _this2.sessionInfo;
            });
          }
          resolve(_this2.sessionInfo);
        }
      });
    }
  }, {
    key: "updateSessionInfo",
    value: function updateSessionInfo(response) {
      if ((0, _lodash["default"])(response)) {
        return;
      }
      var now = new Date().getTime();
      var expiresTimestamp = now + response.expiresIn * 1000;
      this.sessionInfo.accessToken = response.accessToken;
      this.sessionInfo.sessionId = response.sessionId;
      this.sessionInfo.roles = response.roles;
      this.sessionInfo.expiresTimestamp = expiresTimestamp;
      if (!(0, _lodash["default"])(response.store)) {
        this.sessionInfo.store = response.store;
      }
      try {
        this.storageObj.setItem(this.sessionIdStorageName, JSON.stringify(this.sessionInfo));
      } catch (e) {
        console.error(e);
      }
    }
  }]);
  return VidMobSessionManager;
}();
exports["default"] = VidMobSessionManager;