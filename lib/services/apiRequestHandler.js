"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _serviceResult = _interopRequireDefault(require("./serviceResult.js"));
var _serviceError = _interopRequireDefault(require("./serviceError.js"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
var ApiRequestHandler = /*#__PURE__*/function () {
  function ApiRequestHandler(vmApiManager) {
    _classCallCheck(this, ApiRequestHandler);
    this.apiConnections = {};
    this.vmApiManager = vmApiManager;
  }
  _createClass(ApiRequestHandler, [{
    key: "getConnection",
    value: function getConnection(rsrcEndpoint) {
      var apiConnectionKey = rsrcEndpoint.toString();
      if (!this.apiConnections[apiConnectionKey]) {
        this.apiConnections[apiConnectionKey] = this.vmApiManager.resource(rsrcEndpoint);
      }
      return this.apiConnections[apiConnectionKey];
    }

    // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  }, {
    key: "handleApiGet",
    value: function handleApiGet(apiConnection, fullParams, requestParams) {
      if (!apiConnection) {
        return null;
      }
      return apiConnection.get(fullParams).then(function (response) {
        return _serviceResult["default"].withSuccess(response.data, apiConnection, requestParams);
      })["catch"](function (error) {
        throw new _serviceError["default"](error);
      });
    }

    // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  }, {
    key: "handleApiPost",
    value: function handleApiPost(apiConnection, fullParams, requestParams) {
      if (!apiConnection) {
        return null;
      }
      return apiConnection.post(fullParams).then(function (response) {
        return _serviceResult["default"].withSuccess(response.data, apiConnection, requestParams);
      })["catch"](function (error) {
        throw new _serviceError["default"](error);
      });
    }

    // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  }, {
    key: "handleApiPatch",
    value: function handleApiPatch(apiConnection, fullParams, requestParams) {
      if (!apiConnection) {
        return null;
      }
      return apiConnection.patch(fullParams).then(function (response) {
        return _serviceResult["default"].withSuccess(response.data, apiConnection, requestParams);
      })["catch"](function (error) {
        throw new _serviceError["default"](error);
      });
    }

    // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  }, {
    key: "handleApiPut",
    value: function handleApiPut(apiConnection, fullParams, requestParams) {
      if (!apiConnection) {
        return null;
      }
      return apiConnection.put(fullParams).then(function (response) {
        return _serviceResult["default"].withSuccess(response.data, apiConnection, requestParams);
      })["catch"](function (error) {
        throw new _serviceError["default"](error);
      });
    }

    // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  }, {
    key: "handleApiDelete",
    value: function handleApiDelete(apiConnection, fullParams, requestParams) {
      if (!apiConnection) {
        return null;
      }
      return apiConnection["delete"](fullParams).then(function (response) {
        return _serviceResult["default"].withSuccess(response.data, apiConnection, requestParams);
      })["catch"](function (error) {
        throw new _serviceError["default"](error);
      });
    }
  }]);
  return ApiRequestHandler;
}();
var _default = ApiRequestHandler;
exports["default"] = _default;