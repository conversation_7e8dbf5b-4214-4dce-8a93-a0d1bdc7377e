"use strict";

function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _apiRequestHandler = _interopRequireDefault(require("./apiRequestHandler"));
var _resources = _interopRequireDefault(require("./../resources.js"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }
var ProjectService = /*#__PURE__*/function (_ApiRequestHandler) {
  _inherits(ProjectService, _ApiRequestHandler);
  var _super = _createSuper(ProjectService);
  function ProjectService() {
    _classCallCheck(this, ProjectService);
    return _super.apply(this, arguments);
  }
  _createClass(ProjectService, [{
    key: "getProject",
    value: function getProject(projectId) {
      var extraFields = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      var requestParams = {
        projectId: projectId,
        extraFields: extraFields
      };
      return this.handleApiGet(this.getConnection(_resources["default"].project), requestParams, requestParams);
    }
  }, {
    key: "getEditorProjectList",
    value: function getEditorProjectList(editorId, filterParams) {
      var paginationParams = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
      var requestParams = _objectSpread({
        editorId: editorId
      }, filterParams);
      var fullParams = _objectSpread(_objectSpread({}, requestParams), paginationParams);
      return this.handleApiGet(this.getConnection(_resources["default"].editorProject), fullParams, requestParams);
    }
  }, {
    key: "getPartnerProjectList",
    value: function getPartnerProjectList(partnerId, filterParams) {
      var paginationParams = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
      var requestParams = _objectSpread({
        partnerId: partnerId
      }, filterParams);
      var fullParams = _objectSpread(_objectSpread({}, requestParams), paginationParams);
      return this.handleApiGet(this.getConnection(_resources["default"].partnerProject), fullParams, requestParams);
    }
  }, {
    key: "getUserProjectList",
    value: function getUserProjectList(userId, filterParams) {
      var paginationParams = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
      var requestParams = _objectSpread({
        userId: userId
      }, filterParams);
      var fullParams = _objectSpread(_objectSpread({}, requestParams), paginationParams);
      return this.handleApiGet(this.getConnection(_resources["default"].userProject), fullParams, requestParams);
    }
  }, {
    key: "getBriefs",
    value: function getBriefs(projectId) {
      var extraFields = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      var paginationParams = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
      var requestParams = {
        projectId: projectId,
        extraFields: extraFields
      };
      var fullParams = _objectSpread(_objectSpread({}, requestParams), paginationParams);
      return this.handleApiGet(this.getConnection(_resources["default"].projectBriefs), fullParams, requestParams);
    }
  }, {
    key: "getProjectQuestions",
    value: function getProjectQuestions(projectId) {
      var extraFields = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      var paginationParams = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
      var requestParams = {
        projectId: projectId,
        extraFields: extraFields
      };
      var fullParams = _objectSpread(_objectSpread({}, requestParams), paginationParams);
      return this.handleApiGet(this.getConnection(_resources["default"].projectQuestions), fullParams, requestParams);
    }
  }, {
    key: "updateProjectQuestion",
    value: function updateProjectQuestion(projectId, questionId, update) {
      var requestParams = _objectSpread({
        projectId: projectId,
        questionId: questionId
      }, update);
      return this.handleApiPost(this.getConnection(_resources["default"].projectQuestion), requestParams, requestParams);
    }
  }, {
    key: "getProjectSows",
    value: function getProjectSows(projectId) {
      var paginationParams = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      var requestParams = {
        projectId: projectId
      };
      var fullParams = _objectSpread(_objectSpread({}, requestParams), paginationParams);
      return this.handleApiGet(this.getConnection(_resources["default"].projectSow), fullParams, requestParams);
    }
  }, {
    key: "getProjectQuestion",
    value: function getProjectQuestion(projectId, questionId) {
      var requestParams = {
        projectId: projectId,
        questionId: questionId
      };
      return this.handleApiGet(this.getConnection(_resources["default"].projectQuestion), requestParams, requestParams);
    }
  }, {
    key: "getProjectQuestionAttachments",
    value: function getProjectQuestionAttachments(projectId, questionId) {
      var requestParams = {
        projectId: projectId,
        questionId: questionId
      };
      return this.handleApiGet(this.getConnection(_resources["default"].projectQuestionAttachments), requestParams, requestParams);
    }
  }, {
    key: "deleteProjectQuestionAttachment",
    value: function deleteProjectQuestionAttachment(answerAttachmentId) {
      return this.handleApiDelete(this.getConnection(_resources["default"].projectQuestionAttachment), {
        answerAttachmentId: answerAttachmentId
      }, {
        answerAttachmentId: answerAttachmentId
      });
    }
  }, {
    key: "getMilestone",
    value: function getMilestone(projectId) {
      var requestParams = {
        projectId: projectId
      };
      return this.handleApiGet(this.getConnection(_resources["default"].projectMilestone), requestParams, requestParams);
    }
  }, {
    key: "getMilestoneRole",
    value: function getMilestoneRole(projectId) {
      var requestParams = {
        projectId: projectId
      };
      return this.handleApiGet(this.getConnection(_resources["default"].projectMilestoneRole), requestParams, requestParams);
    }
  }, {
    key: "getMilestoneRoleCategory",
    value: function getMilestoneRoleCategory(projectId, roleCategory) {
      var requestParams = {
        projectId: projectId,
        roleCategory: roleCategory
      };
      return this.handleApiGet(this.getConnection(_resources["default"].projectMilestoneRoleCategory), requestParams, requestParams);
    }
  }]);
  return ProjectService;
}(_apiRequestHandler["default"]);
var _default = ProjectService;
exports["default"] = _default;