"use strict";

function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _apiRequestHandler = _interopRequireDefault(require("./apiRequestHandler.js"));
var _resources = _interopRequireDefault(require("./../resources.js"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, "prototype", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }
function _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }
function _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }
function _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === "object" || typeof call === "function")) { return call; } else if (call !== void 0) { throw new TypeError("Derived constructors may only return object or undefined"); } return _assertThisInitialized(self); }
function _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return self; }
function _isNativeReflectConstruct() { if (typeof Reflect === "undefined" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === "function") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }
function _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }
var UserService = /*#__PURE__*/function (_ApiRequestHandler) {
  _inherits(UserService, _ApiRequestHandler);
  var _super = _createSuper(UserService);
  function UserService() {
    _classCallCheck(this, UserService);
    return _super.apply(this, arguments);
  }
  _createClass(UserService, [{
    key: "getUser",
    value: function getUser(userId) {
      return this.handleApiGet(this.getConnection(_resources["default"].user), {
        userId: userId
      }, {
        userId: userId
      });
    }
  }, {
    key: "createUser",
    value: function createUser(userCreationParams) {
      return 'stub' + userCreationParams;
    }
  }, {
    key: "updateUser",
    value: function updateUser(userId, updateParams) {
      var requestParams = _objectSpread({
        userId: userId
      }, updateParams);
      return this.handleApiPost(this.getConnection(_resources["default"].user), requestParams, requestParams);
    }
  }, {
    key: "saveAvatarFile",
    value: function saveAvatarFile(userId, avatarFile) {
      return 'stub' + userId + avatarFile;
    }
  }, {
    key: "beginProcessToBecomeEditor",
    value: function beginProcessToBecomeEditor(userId) {
      return 'stub' + userId;
    }
  }, {
    key: "requestPasswordReset",
    value: function requestPasswordReset(email, target) {
      var requestParams = {
        email: email,
        target: target
      };
      return this.handleApiPost(this.getConnection(_resources["default"].passwordResetRequest), requestParams, requestParams);
    }
  }, {
    key: "resetPassword",
    value: function resetPassword(resetToken, newPassword) {
      var requestParams = {
        resetToken: resetToken,
        newPassword: newPassword
      };
      return this.handleApiPost(this.getConnection(_resources["default"].resetPassword), requestParams, requestParams);
    }
  }, {
    key: "validatePasswordResetToken",
    value: function validatePasswordResetToken(resetToken) {
      var requestParams = {
        resetToken: resetToken
      };
      return this.handleApiGet(this.getConnection(_resources["default"].resetPassword), requestParams, requestParams);
    }
  }]);
  return UserService;
}(_apiRequestHandler["default"]);
var _default = UserService;
exports["default"] = _default;