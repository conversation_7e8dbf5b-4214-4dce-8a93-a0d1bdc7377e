"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _serviceError = _interopRequireDefault(require("./serviceError.js"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
var ServiceResult = /*#__PURE__*/function () {
  function ServiceResult(status, responseData, apiConnection, requestParams, debug) {
    _classCallCheck(this, ServiceResult);
    this.status = status || ServiceResult.FAILURE;
    this.apiConnection = apiConnection || {};
    this.requestParams = requestParams || {};
    this.debug = debug || {};
    this.data = 'default';
    this.pagination = {
      offset: 0,
      perPage: 0,
      nextOffset: 0,
      totalSize: 0,
      queryId: ''
    };
    if (responseData) {
      if (_typeof(responseData) === 'object') {
        if (responseData.status.toUpperCase() === 'OK') {
          this.data = responseData.result;
          this.pagination = responseData.pagination;
        } else if (responseData.response && responseData.response.data) {
          // If the API responds with a payload containing an error message, it looks like this:
          this.data = responseData.response.data.message;
        } else {
          this.data = responseData.error.message;
        }
      } else {
        // If the response wasn't an object then we assume it's a file and send the data through.
        this.data = responseData;
      }
    } else {
      this.data = new Error('The response object returned was null');
    }
  }
  _createClass(ServiceResult, [{
    key: "hasNext",
    value: function hasNext() {
      if (this.pagination === undefined || this.pagination.queryId === '') {
        return false;
      }
      return this.pagination.nextOffset < this.pagination.totalSize;
    }

    // returns a promise that resolves to a ServiceResult??? which is the next page of results
    // returns false if this is called but there's nothing to get (non-paginated result, or end of pages)
  }, {
    key: "next",
    value: function next() {
      var _this = this;
      if (!this.hasNext()) {
        return false;
      }
      var nextParams = _objectSpread(_objectSpread({}, this.requestParams), {
        offset: this.pagination.nextOffset,
        perPage: this.pagination.perPage,
        queryId: this.pagination.queryId
      });
      return this.apiConnection.get(nextParams).then(function (response) {
        return ServiceResult.withSuccess(response.data, _this.apiConnection, _this.requestParams);
      })["catch"](function (error) {
        throw new _serviceError["default"](error);
      });
    }
  }, {
    key: "success",
    value: function success() {
      this.status = ServiceResult.SUCCESS;
    }
  }, {
    key: "isSuccess",
    value: function isSuccess() {
      return this.status === ServiceResult.SUCCESS;
    }
  }, {
    key: "failure",
    value: function failure() {
      this.status = ServiceResult.FAILURE;
    }
  }, {
    key: "isFailure",
    value: function isFailure() {
      return this.status === ServiceResult.FAILURE;
    }
  }], [{
    key: "withFailure",
    value: function withFailure(responseData, apiConnection, requestParams, debug) {
      return new ServiceResult(ServiceResult.FAILURE, responseData, apiConnection, requestParams, debug);
    }
  }, {
    key: "withSuccess",
    value: function withSuccess(responseData, apiConnection, requestParams, debug) {
      return new ServiceResult(ServiceResult.SUCCESS, responseData, apiConnection, requestParams, debug);
    }
  }]);
  return ServiceResult;
}();
_defineProperty(ServiceResult, "SUCCESS", 'success');
_defineProperty(ServiceResult, "FAILURE", 'failure');
var _default = ServiceResult;
exports["default"] = _default;