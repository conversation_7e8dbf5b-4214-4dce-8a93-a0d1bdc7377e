"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = exports.StorageTypes = exports.SessionStoreKey = void 0;
var _axios = _interopRequireDefault(require("axios"));
var _lodash = _interopRequireDefault(require("lodash.isnil"));
var _lodash2 = _interopRequireDefault(require("lodash.isfunction"));
var _jsCookie = _interopRequireDefault(require("js-cookie"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
// by not setting an expiration, cookie is a sessionCookie by default. see: https://github.com/js-cookie/js-cookie#expires

var StorageTypes = {
  LocalStorage: 'localStorage',
  SessionStorage: 'sessionStorage',
  CookieStorage: 'cookieStorage'
};
exports.StorageTypes = StorageTypes;
var ONE_HOUR_IN_MS = 60 * 60 * 1000;
var SessionStoreKey = 'vm_session';
exports.SessionStoreKey = SessionStoreKey;
function storageAvailable(type) {
  if (type === StorageTypes.CookieStorage) {
    return navigator.cookieEnabled;
  }
  var storage = window[type];
  var x = '__storage_test__';
  try {
    storage.setItem(x, x);
    storage.removeItem(x);
    return true;
  } catch (e) {
    return e instanceof DOMException && (
    // everything except Firefox
    e.code === 22 ||
    // Firefox
    e.code === 1014 ||
    // test name field too, because code might not be present
    // everything except Firefox
    e.name === 'QuotaExceededError' ||
    // Firefox
    e.name === 'NS_ERROR_DOM_QUOTA_REACHED') &&
    // acknowledge QuotaExceededError only if there's something already stored
    storage.length !== 0;
  }
}
var VidMobSessionManager = /*#__PURE__*/function () {
  function VidMobSessionManager(routeToExpectedPage) {
    var sessionTimeoutMin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    _classCallCheck(this, VidMobSessionManager);
    _defineProperty(this, "sessionInfo", void 0);
    this.sessionIdStorageName = SessionStoreKey;
    this.routeToExpectedPage = routeToExpectedPage;
    this.sessionTimeoutMin = sessionTimeoutMin;
    this.storageObj = null;
    this.sessionLastRequestSentTimestamp = null;
    this.sessionInfo = {
      sessionId: null,
      accessToken: null,
      expiresTimestamp: null,
      roles: [],
      store: false
    };
    this.clientToldToLogout = false;
    this.assignStorage();
    this.resetSessionIdleTimeout();
  }

  // attempts all storage options, prioritizing cookies > session > local if they are available
  _createClass(VidMobSessionManager, [{
    key: "assignStorage",
    value: function assignStorage() {
      this.checkAndSetStorage(StorageTypes.LocalStorage);
      this.checkAndSetStorage(StorageTypes.SessionStorage);
      this.checkAndSetStorage(StorageTypes.CookieStorage);
    }
  }, {
    key: "setSessionTimeoutMin",
    value: function setSessionTimeoutMin(sessionTimeoutMin) {
      this.sessionTimeoutMin = sessionTimeoutMin;
      this.sessionLastRequestSentTimestamp = Date.now(); // Reset idle timeout when timeout is changed
      this.resetSessionIdleTimeout();
    }
  }, {
    key: "isSessionExpired",
    value: function isSessionExpired() {
      var sessionTimeoutMs = this.sessionTimeoutMin ? this.sessionTimeoutMin * 60 * 1000 : ONE_HOUR_IN_MS; // 1 hour by default
      console.log("***** session timeout min: ".concat(sessionTimeoutMs / 60000));
      return this.sessionLastRequestSentTimestamp && this.sessionLastRequestSentTimestamp <= Date.now() - sessionTimeoutMs;
    }
  }, {
    key: "resetSessionIdleTimeout",
    value: function resetSessionIdleTimeout() {
      if (this.isSessionExpired()) {
        this.clearSession();
        console.log('Session timed out');
        return;
      }
      this.sessionLastRequestSentTimestamp = Date.now();
    }
  }, {
    key: "checkAndSetStorage",
    value: function checkAndSetStorage(storageType) {
      if (!storageAvailable(storageType)) {
        return;
      }
      if (storageType === StorageTypes.CookieStorage) {
        var sessionCookie = _jsCookie["default"].get(this.sessionIdStorageName);
        if (!sessionCookie) {
          return;
        }
        this.sessionInfo = JSON.parse(sessionCookie);
        this.setStorage(storageType);
        return;
      }
      var sessionItem = window[storageType].getItem(this.sessionIdStorageName);
      if (!sessionItem) {
        return;
      }
      this.sessionInfo = JSON.parse(sessionItem);
      this.setStorage(storageType);
    }
  }, {
    key: "setStorage",
    value: function setStorage(type) {
      if (!storageAvailable(type)) {
        return;
      }
      if (type === StorageTypes.CookieStorage) {
        this.storageObj = {
          removeItem: _jsCookie["default"].remove,
          setItem: function setItem(key, value) {
            _jsCookie["default"].set(key, value, {
              sameSite: 'none',
              secure: true
            });
          }
        };
        return;
      }
      this.storageObj = window[type];
    }
  }, {
    key: "getToken",
    value: function getToken() {
      return this.sessionInfo.accessToken;
    }
  }, {
    key: "clearSession",
    value: function clearSession() {
      this.sessionLastRequestSentTimestamp = null;
      if (this.storageObj !== null) {
        this.storageObj.removeItem(this.sessionIdStorageName);
      }
      this.sessionInfo = {
        sessionId: null,
        accessToken: null,
        expiresTimestamp: null,
        roles: [],
        store: false
      };
    }
  }, {
    key: "sessionExists",
    value: function sessionExists() {
      return Boolean(this.sessionInfo) && this.sessionInfo.sessionId !== null;
    }
  }, {
    key: "triggerUserRedirect",
    value: function triggerUserRedirect() {
      if (this.routeToExpectedPage && (0, _lodash2["default"])(this.routeToExpectedPage) && !this.clientToldToLogout) {
        this.clientToldToLogout = true;
        this.routeToExpectedPage(this.clientToldToLogout);
      }
    }
  }, {
    key: "logoutPromise",
    value: function logoutPromise() {
      var _this = this;
      return new Promise(function (resolve, reject) {
        _axios["default"].post('/api/logout', {
          sessionId: _this.sessionInfo.sessionId
        }, {
          timeout: 10000
        }).then(function (response) {
          try {
            _this.clearSession();
          } catch (error) {
            console.log('logout error' + error);
          }
          resolve(response.data);
        })["catch"](function (error) {
          console.error('Error:', error);
          reject(error);
        });
      });
    }
  }, {
    key: "logout",
    value: function logout() {
      var _this2 = this;
      return (0, _axios["default"])({
        method: 'post',
        url: '/api/logout',
        timeout: 30000,
        data: {
          sessionId: this.sessionInfo.sessionId
        }
      }).then(function (response) {
        console.log('logout sucessfully');
        return response;
      })["catch"](function (err) {
        console.log('logout error');
        console.log(err);
        throw err;
      })["finally"](function () {
        console.log('logout finally end');
        _this2.clearSession();
      });
    }
  }, {
    key: "login",
    value: function login(userCredentials) {
      var _this3 = this;
      this.clientToldToLogout = false;
      var userResponse = {};
      userResponse.store = userCredentials ? userCredentials.store : undefined;
      if (userResponse.store) {
        this.setStorage(userResponse.store);
      } else {
        this.setStorage(StorageTypes.SessionStorage);
      }
      return (0, _axios["default"])({
        method: 'post',
        url: '/api/login',
        data: {
          username: userCredentials.username,
          password: userCredentials.password,
          twoFactorAuthPassword: userCredentials.twoFactorAuthPassword
        }
      }).then(function (response) {
        userResponse.sessionId = response.data.result.sessionId;
        userResponse.accessToken = response.data.result.accessToken;
        userResponse.expiresIn = response.data.result.expiresIn;
        userResponse.roles = response.data.result.roles;
        _this3.updateSessionInfo(userResponse);
        return response;
      });
    }
  }, {
    key: "ssoSession",
    value: function ssoSession(userCredentials) {
      var _this4 = this;
      this.clientToldToLogout = false;
      var userResponse = {};
      userResponse.store = userCredentials ? userCredentials.store : undefined;
      if (userResponse.store) {
        this.setStorage(userResponse.store);
      } else {
        this.setStorage(StorageTypes.SessionStorage);
      }
      return (0, _axios["default"])({
        method: 'post',
        url: '/api/sso/session',
        data: {
          code: userCredentials.code
        }
      }).then(function (response) {
        userResponse.sessionId = response.data.result.sessionId;
        userResponse.accessToken = response.data.result.accessToken;
        userResponse.expiresIn = response.data.result.expiresIn;
        userResponse.roles = [];
        _this4.updateSessionInfo(userResponse);
        return response;
      })["catch"](function (error) {
        throw error;
      });
    }
  }, {
    key: "requestNewToken",
    value: function requestNewToken() {
      var _this5 = this;
      return (0, _axios["default"])({
        method: 'post',
        url: '/api/session',
        data: {
          sessionId: this.sessionInfo.sessionId
        }
      }).then(function (response) {
        return response;
      })["catch"](function (error) {
        _this5.triggerUserRedirect();
        throw error;
      });
    }

    /**
     * Use to determine if the user has a valid session.
     *
     * @returns {Promise<Resolves when session valid. Reject when not.>}
     */
  }, {
    key: "sessionIsValid",
    value: function sessionIsValid() {
      var _this6 = this;
      this.resetSessionIdleTimeout();
      return new Promise(function (resolve, reject) {
        if (_this6.sessionInfo.sessionId === null) {
          reject(new Error('No valid session'));
        } else {
          var now = new Date().getTime();
          if (_this6.sessionInfo.expiresTimestamp < now) {
            _this6.requestNewToken().then(function (response) {
              _this6.updateSessionInfo(response.data.result);
              resolve(_this6.sessionInfo);
            })["catch"](function (err) {
              reject(err);
            });
          } else {
            resolve(_this6.sessionInfo);
          }
        }
      });
    }

    /**
     * This function is meant to be used when making API calls and we need to redirect a user by calling the callBack
     * function provided with the instance of this class. Typically this should only be used by the client-session-manager
     * itself.
     *
     * @returns {Promise<Resolves when session valid. Reject when not.>}
     */
  }, {
    key: "sessionIsValidWithRedirect",
    value: function sessionIsValidWithRedirect() {
      var _this7 = this;
      this.resetSessionIdleTimeout();
      return new Promise(function (resolve, reject) {
        if (_this7.sessionInfo.sessionId === null) {
          reject(new Error('No valid session'));
          _this7.triggerUserRedirect();
        } else {
          var now = new Date().getTime();
          if (_this7.sessionInfo.expiresTimestamp < now) {
            _this7.requestNewToken().then(function (response) {
              _this7.updateSessionInfo(response.data.result);
              resolve(_this7.sessionInfo);
            })["catch"](function (err) {
              if (err && err.response) {
                console.error('Error trying to get new session token.', err);
                reject(err);
              } else if (err && err.request) {
                /**
                 * In the case of a network failure during a users check to see if their session is valid
                 * we do not want to just reject the session. If we do the user will be logged out even if they
                 * should not be.
                 */
                console.error('Network error trying to get new session token.', err);
                resolve(_this7.sessionInfo);
              } else {
                /**
                 * If we can not determine the type of error we are catching we will get here.
                 */
                console.error('Unknown error trying to get new session token.', err);
                reject(err);
              }
            });
          } else {
            resolve(_this7.sessionInfo);
          }
        }
      });
    }
  }, {
    key: "updateSessionInfo",
    value: function updateSessionInfo(response) {
      if ((0, _lodash["default"])(response)) {
        return;
      }
      var now = new Date().getTime();
      var expiresTimestamp = now + response.expiresIn * 1000;
      this.sessionInfo.accessToken = response.accessToken;
      this.sessionInfo.sessionId = response.sessionId;
      this.sessionInfo.roles = response.roles;
      this.sessionInfo.expiresTimestamp = expiresTimestamp;
      this.sessionLastRequestSentTimestamp = Date.now();
      if (!(0, _lodash["default"])(response.store)) {
        this.sessionInfo.store = response.store;
      }
      if (this.storageObj !== null) {
        this.storageObj.setItem(this.sessionIdStorageName, JSON.stringify(this.sessionInfo));
      }
    }
  }]);
  return VidMobSessionManager;
}();
exports["default"] = VidMobSessionManager;