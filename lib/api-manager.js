"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _axios = _interopRequireDefault(require("axios"));
var _api = _interopRequireDefault(require("./api"));
var _resources = _interopRequireDefault(require("./resources"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }
function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }
function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, "prototype", { writable: false }); return Constructor; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
var VidMobApiManager = /*#__PURE__*/function () {
  function VidMobApiManager(config, vmSessionManager) {
    _classCallCheck(this, VidMobApiManager);
    this.sessionManager = vmSessionManager;
    this.baseApiUrl = config.apiUrl;
    this.apiVersion = config.apiVersion;
    this.lastTokenStored = null;
    this.axiosInstance = _axios["default"].create({
      baseURL: this.baseApiUrl,
      headers: {
        'VidMob-App-Version': this.apiVersion
      }
    });
  }
  _createClass(VidMobApiManager, [{
    key: "setAuthHeader",
    value: function setAuthHeader(token) {
      if (this.lastTokenStored === null || this.lastTokenStored !== token) {
        this.lastTokenStored = token;
        this.axiosInstance.defaults.headers.common.Authorization = 'Bearer ' + token;
      }
    }
  }, {
    key: "resourceList",
    value: function resourceList() {
      return Object.keys(_resources["default"]).sort();
    }
  }, {
    key: "resource",
    value: function resource(endPoint) {
      var authNeeded = !endPoint.includes('/noauth/');
      return new _api["default"](this, '/api' + endPoint, authNeeded);
    }

    // Used to return an empty promise that resolves.
  }, {
    key: "canceledRequestPromise",
    value: function canceledRequestPromise() {
      return new Promise(function (resolve) {
        resolve({});
      });
    }
  }, {
    key: "getInstance",
    value: function getInstance(tokenRequired) {
      var _this = this;
      return new Promise(function (resolve) {
        if (tokenRequired) {
          _this.sessionManager.sessionIsValidWithRedirect().then(function (response) {
            _this.setAuthHeader(response.accessToken);
            resolve(_this.axiosInstance);
          })["catch"](function () {
            // Because we want the user to be redirected to the login page we cancel the API request being made
            // and return an empty promise. If we reject the call it results in the client showing an API error and
            // not the session expired message.
            _this.canceledRequestPromise();
          });
        } else {
          resolve(_this.axiosInstance);
        }
      });
    }
  }]);
  return VidMobApiManager;
}();
exports["default"] = VidMobApiManager;