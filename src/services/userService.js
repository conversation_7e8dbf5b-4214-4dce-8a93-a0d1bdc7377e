import ApiRequest<PERSON>andler from './apiRequestHandler.js';
import resourceEndpoints from './../resources.js';

class UserService extends ApiRequestHandler {
  getUser(userId) {
    return this.handleApiGet(this.getConnection(resourceEndpoints.user),
      {userId}, {userId});
  }

  createUser(userCreationParams) {
    return 'stub' + userCreationParams;
  }

  updateUser(userId, updateParams) {
    const requestParams = {userId, ...updateParams};
    return this.handleApiPost(this.getConnection(resourceEndpoints.user),
      requestParams, requestParams);
  }

  saveAvatarFile(userId, avatarFile) {
    return 'stub' + userId + avatarFile;
  }

  beginProcessToBecomeEditor(userId) {
    return 'stub' + userId;
  }

  requestPasswordReset(email, target) {
    const requestParams = {email, target};
    return this.handleApiPost(this.getConnection(resourceEndpoints.passwordResetRequest),
      requestParams, requestParams);
  }

  resetPassword(resetToken, newPassword) {
    const requestParams = {resetToken, newPassword};
    return this.handleApiPost(this.getConnection(resourceEndpoints.resetPassword),
      requestParams, requestParams);
  }

  validatePasswordResetToken(resetToken) {
    const requestParams = {resetToken};
    return this.handleApiGet(this.getConnection(resourceEndpoints.resetPassword),
      requestParams, requestParams);
  }
}

export default UserService;
