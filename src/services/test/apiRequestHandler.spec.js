import ApiRequestHandler from '../apiRequestHandler.js';

// to run this, use
// $ npm test -t serviceResult

// =========================================================

// can use this as a template
/*
test("verify testing is working", () => {
    expect(1+3).toBe(4);
});
*/

// =========================================================
// =========================================================
// =========================================================

test('default service result', () => {
  // setup
  const arh = new ApiRequestHandler();

  // expect
  expect(arh.handleApiGet()).toBeNull();
});

// =========================================================

