import ServiceResult from '../serviceResult.js';

// to run this, use
// $ npm test -t serviceResult

// =========================================================

// can use this as a template
/*
test("verify testing is working", () => {
    expect(1+3).toBe(4);
});
*/

// =========================================================
// =========================================================
// =========================================================

test('default service result', () => {
  // setup
  const sr = new ServiceResult();

  // expect
  expect(sr.isSuccess()).toBe(false);
  expect(sr.isFailure()).toBe(true);
  expect(sr.data).toEqual(expect.any(Error));
  expect(sr.debug).toEqual({});
});

// =========================================================

test('service result status setters', () => {
  // setup
  const sr = new ServiceResult();

  // expect
  expect(sr.isSuccess()).toBe(false);
  expect(sr.isFailure()).toBe(true);

  // when
  sr.success();
  // then
  expect(sr.isSuccess()).toBe(true);
  expect(sr.isFailure()).toBe(false);

  // when
  sr.failure();
  // then
  expect(sr.isSuccess()).toBe(false);
  expect(sr.isFailure()).toBe(true);
});

// =========================================================

test('basic withSuccess', () => {
  // setup
  const respData = {
    status: 'OK', // NOTE: also check that uppercase version of ok works
    result: {
      id: 521,
      projectId: 1,
      name: 'name a',
      userId: 20
    }
  };
  const apiConnection = 'an api connection';
  const requestParams = {
    projectId: 1,
    userId: 20,
    sortKey: 'name'
  };

  // expect

  // when
  const sr = ServiceResult.withSuccess(respData, apiConnection, requestParams);
  // then
  expect(sr.isSuccess()).toBe(true);
  expect(sr.data).toEqual({
    id: 521,
    projectId: 1,
    name: 'name a',
    userId: 20
  });
  expect(sr.pagination).toBeUndefined();
  expect(sr.apiConnection).toEqual(apiConnection);
  expect(sr.requestParams).toEqual(requestParams);
  expect(sr.hasNext()).toEqual(false);
});

// =========================================================

test('paginated withSuccess', () => {
  // setup
  const respData = {
    status: 'ok', // NOTE: also check that lowercase version of ok works
    result: [
      {
        id: 521,
        projectId: 1,
        name: 'name a',
        userId: 20
      },
      {
        id: 525,
        projectId: 1,
        name: 'name b',
        userId: 20
      }
    ],
    pagination: {
      offset: 0,
      perPage: 2,
      nextOffset: 2,
      totalSize: 8,
      queryId: '123456-abcdef'
    }
  };
  const apiConnection = 'an api connection';
  const requestParams = {
    projectId: 1,
    userId: 20,
    sortKey: 'name'
  };

  // expect

  // when
  const sr = ServiceResult.withSuccess(respData, apiConnection, requestParams);
  // then
  expect(sr.isSuccess()).toBe(true);
  expect(sr.data).toEqual([
    {
      id: 521,
      projectId: 1,
      name: 'name a',
      userId: 20
    },
    {
      id: 525,
      projectId: 1,
      name: 'name b',
      userId: 20
    }
  ]);
  expect(sr.pagination).toEqual({
    offset: 0,
    perPage: 2,
    nextOffset: 2,
    totalSize: 8,
    queryId: '123456-abcdef'
  });
  expect(sr.apiConnection).toEqual(apiConnection);
  expect(sr.requestParams).toEqual(requestParams);
  expect(sr.hasNext()).toEqual(true);
});

// =========================================================

test('basic withFailure', () => {
  // setup
  const respData = {
    status: 'error',
    error: {
      code: 'vidmob.project.accessViolation',
      type: 'accessViolation',
      system: 'project',
      message: 'Access denied.',
      data: {
        item: 'project',
        value: '35'
      }
    }
  };
  const apiConnection = 'an api connection';
  const requestParams = {
    projectId: 1,
    userId: 20,
    sortKey: 'name'
  };

  // expect

  // when
  const sr = ServiceResult.withFailure(respData, apiConnection, requestParams);
  // then
  expect(sr.isFailure()).toBe(true);
  expect(sr.data).toEqual('Access denied.');
  expect(sr.pagination).toEqual({nextOffset: 0, offset: 0, perPage: 0, queryId: '', totalSize: 0});
  expect(sr.apiConnection).toEqual(apiConnection);
  expect(sr.requestParams).toEqual(requestParams);
  expect(sr.hasNext()).toEqual(false);
});

// =========================================================

// NOTE: can't figure out how to mock the apiConnection's 'get' call - might need a sep package
// to do that kind of thing (moxios?)

// test('interate next', () => {
//   // setup
//   const respData = {
//         "status": "OK",
//         "result": [
//             {
//                 "id": 521,
//                 "projectId": 1,
//                 "name": "name a",
//                 "userId": 20,
//             },
//             {
//                 "id": 525,
//                 "projectId": 1,
//                 "name": "name b",
//                 "userId": 20,
//             },
//         ],
//         "pagination": {
//             "offset": 0,
//             "perPage": 2,
//             "nextOffset": 2,
//             "totalSize": 8,
//             "queryId": "123456-abcdef"
//         }
//     };
//
//     // mock the api get request
//     const apiConnection = {
//         get: function() {
//             return Promise.resolve({
//                         data: {
//                             "status": "OK",
//                             "result": [
//                                 {
//                                     "id": 527,
//                                     "projectId": 1,
//                                     "name": "name c",
//                                     "userId": 20,
//                                 },
//                                 {
//                                     "id": 529,
//                                     "projectId": 1,
//                                     "name": "name d",
//                                     "userId": 20,
//                                 },
//                             ],
//                             "pagination": {
//                                 "offset": 2,
//                                 "perPage": 2,
//                                 "nextOffset": 4,
//                                 "totalSize": 8,
//                                 "queryId": "123456-abcdef"
//                             }
//                         }
//                     }
//                 }
//             });
//         }
//     };
//   const requestParams = {
//       'projectId':1,
//       'userId':20,
//       'sortKey':'name'
//   };
//
//   const sr = ServiceResult.withSuccess(respData, apiConnection, requestParams);
//
//   // expect
//
//   // when
//   const srNext = sr.next();
//   // then
//   expect(srNext.isSuccess()).toBe(true);
//   expect(srNext.data).toEqual([
//       {
//           "id": 527,
//           "projectId": 1,
//           "name": "name c",
//           "userId": 20,
//       },
//       {
//           "id": 529,
//           "projectId": 1,
//           "name": "name d",
//           "userId": 20,
//       },
//   ]);
//   expect(srNext.pagination).toEqual({
//       "offset": 2,
//       "perPage": 2,
//       "nextOffset": 4,
//       "totalSize": 8,
//       "queryId": "123456-abcdef"
//   });
//   expect(srNext.apiConnection).toEqual(apiConnection);
//   expect(srNext.requestParams).toEqual(requestParams);
//   expect(srNext.hasNext()).toEqual(true);
// });

// =========================================================

