import ServiceResult from './serviceResult.js';
import ServiceError from './serviceError.js';

class ApiRequestHandler {
  constructor(vmApiManager) {
    this.apiConnections = {};
    this.vmApiManager = vmApiManager;
  }

  getConnection(rsrcEndpoint) {
    const apiConnectionKey = rsrcEndpoint.toString();
    if (!this.apiConnections[apiConnectionKey]) {
      this.apiConnections[apiConnectionKey] = this.vmApiManager.resource(rsrcEndpoint);
    }
    return this.apiConnections[apiConnectionKey];
  }

  // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  handleApiGet(apiConnection, fullParams, requestParams) {
    if (!apiConnection) {
      return null;
    }
    return apiConnection.get(fullParams).then(response => {
      return ServiceResult.withSuccess(response.data, apiConnection, requestParams);
    }).catch(error => {
      throw new ServiceError(error);
    });
  }

  // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  handleApiPost(apiConnection, fullParams, requestParams) {
    if (!apiConnection) {
      return null;
    }
    return apiConnection.post(fullParams).then(response => {
      return ServiceResult.withSuccess(response.data, apiConnection, requestParams);
    }).catch(error => {
      throw new ServiceError(error);
    });
  }

  // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  handleApiPatch(apiConnection, fullParams, requestParams) {
    if (!apiConnection) {
      return null;
    }
    return apiConnection.patch(fullParams).then(response => {
      return ServiceResult.withSuccess(response.data, apiConnection, requestParams);
    }).catch(error => {
      throw new ServiceError(error);
    });
  }

  // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  handleApiPut(apiConnection, fullParams, requestParams) {
    if (!apiConnection) {
      return null;
    }
    return apiConnection.put(fullParams).then(response => {
      return ServiceResult.withSuccess(response.data, apiConnection, requestParams);
    }).catch(error => {
      throw new ServiceError(error);
    });
  }

  // NOTE: requestParams are cached in the result; generally fullParams === requestParams + paginationParams
  handleApiDelete(apiConnection, fullParams, requestParams) {
    if (!apiConnection) {
      return null;
    }
    return apiConnection.delete(fullParams).then(response => {
      return ServiceResult.withSuccess(response.data, apiConnection, requestParams);
    }).catch(error => {
      throw new ServiceError(error);
    });
  }
}

export default ApiRequestHandler;
