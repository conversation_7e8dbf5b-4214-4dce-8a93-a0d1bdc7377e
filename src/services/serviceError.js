class ServiceError extends Error {
  constructor(baseError) {
    super();
    this.baseError = '';
    this.responseStatus = '';
    this.responseData = '';
    this.responseError = '';
    this.responseErrorCode = '';
    this.responseErrorData = '';
    this.responseErrorMessage = '';

    // defaults
    this.message = 'Unknown error';
    this.status = '';

    if (baseError) {
      this.baseError = baseError;
      if (baseError.message) {
        this.message = baseError.message;
      }

      if (baseError.response) {
        if (baseError.response.status) {
          this.responseStatus = baseError.response.status;
          this.status = baseError.response.status;
        }
        if (baseError.response.data) {
          this.responseData = baseError.response.data;
          if (baseError.response.data.error) {
            this.responseError = baseError.response.data.error;
            if (baseError.response.data.error.code) {
              this.responseErrorCode = baseError.response.data.error.code;
            }
            if (baseError.response.data.error.data) {
              this.responseErrorData = baseError.response.data.error.data;
            }
            if (baseError.response.data.error.message) {
              this.responseErrorMessage = baseError.response.data.error.message;
            }
          }
        }
      }
    }
  }
}

export default ServiceError;
