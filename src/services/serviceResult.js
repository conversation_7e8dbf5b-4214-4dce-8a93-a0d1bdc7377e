import ServiceError from './serviceError.js';

class ServiceResult {
    static SUCCESS = 'success';

    static FAILURE = 'failure';

    static withFailure(responseData, apiConnection, requestParams, debug) {
      return new ServiceResult(ServiceResult.FAILURE, responseData, apiConnection, requestParams, debug);
    }

    static withSuccess(responseData, apiConnection, requestParams, debug) {
      return new ServiceResult(ServiceResult.SUCCESS, responseData, apiConnection, requestParams, debug);
    }

    constructor(status, responseData, apiConnection, requestParams, debug) {
      this.status = status || ServiceResult.FAILURE;
      this.apiConnection = apiConnection || {};
      this.requestParams = requestParams || {};
      this.debug = debug || {};

      this.data = 'default';
      this.pagination = {
        offset: 0,
        perPage: 0,
        nextOffset: 0,
        totalSize: 0,
        queryId: ''
      };

      if (responseData) {
        if (typeof responseData === 'object') {
          if (responseData.status.toUpperCase() === 'OK') {
            this.data = responseData.result;
            this.pagination = responseData.pagination;
          } else if (responseData.response && responseData.response.data) {
            // If the API responds with a payload containing an error message, it looks like this:
            this.data = responseData.response.data.message;
          } else {
            this.data = responseData.error.message;
          }
        } else {
          // If the response wasn't an object then we assume it's a file and send the data through.
          this.data = responseData;
        }
      } else {
        this.data = new Error('The response object returned was null');
      }
    }

    hasNext() {
      if ((this.pagination === undefined) || (this.pagination.queryId === '')) {
        return false;
      }
      return this.pagination.nextOffset < this.pagination.totalSize;
    }

    // returns a promise that resolves to a ServiceResult??? which is the next page of results
    // returns false if this is called but there's nothing to get (non-paginated result, or end of pages)
    next() {
      if (!this.hasNext()) {
        return false;
      }
      const nextParams = {...this.requestParams, ...{
        offset: this.pagination.nextOffset,
        perPage: this.pagination.perPage,
        queryId: this.pagination.queryId
      }};
      return this.apiConnection.get(nextParams).then(response => {
        return ServiceResult.withSuccess(response.data, this.apiConnection, this.requestParams);
      }).catch(error => {
        throw new ServiceError(error);
      });
    }

    success() {
      this.status = ServiceResult.SUCCESS;
    }

    isSuccess() {
      return this.status === ServiceResult.SUCCESS;
    }

    failure() {
      this.status = ServiceResult.FAILURE;
    }

    isFailure() {
      return this.status === ServiceResult.FAILURE;
    }
}

export default ServiceResult;
