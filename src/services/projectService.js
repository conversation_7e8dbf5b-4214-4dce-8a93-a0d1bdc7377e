/*
This offers a basic template / example for implementing a service.
*/
import ApiRequestHandler from './apiRequestHandler';
import resourceEndpoints from './../resources.js';

class ProjectService extends ApiRequestHandler {
  getProject(projectId, extraFields = null) {
    const requestParams = {projectId, extraFields};
    return this.handleApiGet(this.getConnection(resourceEndpoints.project),
      requestParams, requestParams);
  }

  getEditorProjectList(editorId, filterParams, paginationParams = null) {
    const requestParams = {editorId, ...filterParams};
    const fullParams = {...requestParams, ...paginationParams};
    return this.handleApiGet(this.getConnection(resourceEndpoints.editorProject),
      fullParams, requestParams);
  }

  getPartnerProjectList(partnerId, filterParams, paginationParams = null) {
    const requestParams = {partnerId, ...filterParams};
    const fullParams = {...requestParams, ...paginationParams};
    return this.handleApiGet(this.getConnection(resourceEndpoints.partnerProject),
      fullParams, requestParams);
  }

  getUserProjectList(userId, filterParams, paginationParams = null) {
    const requestParams = {userId, ...filterParams};
    const fullParams = {...requestParams, ...paginationParams};
    return this.handleApiGet(this.getConnection(resourceEndpoints.userProject),
      fullParams, requestParams);
  }

  getBriefs(projectId, extraFields = null, paginationParams = null) {
    const requestParams = {projectId, extraFields};
    const fullParams = {...requestParams, ...paginationParams};
    return this.handleApiGet(this.getConnection(resourceEndpoints.projectBriefs),
      fullParams, requestParams);
  }

  getProjectQuestions(projectId, extraFields = null, paginationParams = null) {
    const requestParams = {projectId, extraFields};
    const fullParams = {...requestParams, ...paginationParams};
    return this.handleApiGet(this.getConnection(resourceEndpoints.projectQuestions),
      fullParams, requestParams);
  }

  updateProjectQuestion(projectId, questionId, update) {
    const requestParams = {projectId, questionId, ...update};
    return this.handleApiPost(this.getConnection(resourceEndpoints.projectQuestion),
      requestParams, requestParams);
  }

  getProjectSows(projectId, paginationParams = null) {
    const requestParams = {projectId};
    const fullParams = {...requestParams, ...paginationParams};
    return this.handleApiGet(this.getConnection(resourceEndpoints.projectSow),
      fullParams, requestParams);
  }

  getProjectQuestion(projectId, questionId) {
    const requestParams = {projectId, questionId};
    return this.handleApiGet(this.getConnection(resourceEndpoints.projectQuestion),
      requestParams, requestParams);
  }

  getProjectQuestionAttachments(projectId, questionId) {
    const requestParams = {projectId, questionId};
    return this.handleApiGet(this.getConnection(resourceEndpoints.projectQuestionAttachments),
      requestParams, requestParams);
  }

  deleteProjectQuestionAttachment(answerAttachmentId) {
    return this.handleApiDelete(this.getConnection(resourceEndpoints.projectQuestionAttachment), {answerAttachmentId}, {answerAttachmentId});
  }

  getMilestone(projectId) {
    const requestParams = {projectId};
    return this.handleApiGet(this.getConnection(resourceEndpoints.projectMilestone),
      requestParams, requestParams);
  }

  getMilestoneRole(projectId) {
    const requestParams = {projectId};
    return this.handleApiGet(this.getConnection(resourceEndpoints.projectMilestoneRole),
      requestParams, requestParams);
  }

  getMilestoneRoleCategory(projectId, roleCategory) {
    const requestParams = {projectId, roleCategory};
    return this.handleApiGet(this.getConnection(resourceEndpoints.projectMilestoneRoleCategory),
      requestParams, requestParams);
  }
}

export default ProjectService;
