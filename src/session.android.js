import axios from 'axios';
import isNil from 'lodash.isnil';
import {AsyncStorage} from 'react-native';

function storageAvailable(type) {
  const storage = type;
  const x = '__storage_test__';
  try {
    storage.setItem(x, x);
    storage.removeItem(x);
    return true;
  } catch (e) {
    console.error(e);
    return e.name;
  }
}

export default class VidMobSessionManager {
  constructor(config) {
    // this.sessionApiUrl = config.authApiAndroidStudioURL; //for Android Studio emulator
    this.sessionApiUrl = config.authApiThirdPartyURL; // for non-Android Studio emulators
    this.asyncStorage = AsyncStorage;
    this.storageObj = AsyncStorage;
    this.sessionIdStorageName = 'vm_session';
    this.sessionInfo = {
      sessionId: null,
      accessToken: null,
      expiresTimestamp: null,
      roles: [],
      store: false
    };
  }

  sessionInStorage(storageType) {
    if (storageType.getItem(this.sessionIdStorageName)) {
      this.sessionInfo = storageType.getItem(this.sessionIdStorageName);
      return true;
    }
    return false;
  }

  setStorageType(type) {
    this.storageObj = type;
  }

  loadSessionInfo() {
    if (storageAvailable(this.asyncStorage)) {
      this.setStorageType(this.asyncStorage);
    }
  }

  getToken() {
    return this.sessionInfo.accessToken;
  }

  logout() {
    if (this.storageObj !== null) {
      this.storageObj.removeItem(this.sessionIdStorageName);
    }
  }

  login(userCredentials) {
    const userResponse = {};
    return axios({
      method: 'post',
      url: this.sessionApiUrl + '/api/login',
      data: {
        username: userCredentials.username,
        password: userCredentials.password
      }
    })
      .then(response => {
        userResponse.sessionId = response.data.result.sessionId;
        userResponse.accessToken = response.data.result.accessToken;
        userResponse.roles = response.data.result.roles;
        userResponse.expiresIn = response.data.result.expiresIn;
        this.updateSessionInfo(userResponse);
        return response;
      })
      .catch(err => console.log(err));
  }

  requestNewToken() {
    return axios({
      method: 'get',
      url: this.sessionApiUrl + '/api/session/' + this.sessionInfo.sessionId
    }).then(response => {
      return response;
    }).catch(error => {
      return error;
    });
  }

  sessionIsValid() {
    return new Promise((resolve, reject) => {
      if (this.sessionInfo.sessionId === null) {
        reject(new Error('No valid session'));
      } else {
        const now = (new Date()).getTime();
        if (this.sessionInfo.expiresTimestamp < now) {
          return this.requestNewToken().then(response => {
            this.updateSessionInfo(response.data.result);
            return this.sessionInfo;
          });
        }
        resolve(this.sessionInfo);
      }
    });
  }

  updateSessionInfo(response) {
    if (isNil(response)) {
      return;
    }
    const now = (new Date()).getTime();
    const expiresTimestamp = (now + (response.expiresIn * 1000));
    this.sessionInfo.accessToken = response.accessToken;
    this.sessionInfo.sessionId = response.sessionId;
    this.sessionInfo.roles = response.roles;
    this.sessionInfo.expiresTimestamp = expiresTimestamp;
    if (!isNil(response.store)) {
      this.sessionInfo.store = response.store;
    }
    try {
      this.storageObj.setItem(this.sessionIdStorageName, JSON.stringify(this.sessionInfo));
    } catch (e) {
      console.error(e);
    }
  }
}
