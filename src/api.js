import isNil from 'lodash.isnil';

export default class VidMobAuthApi {
  constructor(vmApiManager, url, tokenRequired) {
    this.vmApiManager = vmApiManager;
    this.url = url;
    this.tokenRequired = tokenRequired || false;
  }

  prepareUrl(url, urlPathFills) {
    const pathsToReplace = this.url.match(/:([a-zA-Z0-9]+)/g);
    if (isNil(pathsToReplace)) {
      return url;
    }
    pathsToReplace.forEach(path => {
      const pattern = new RegExp(path);
      const value = urlPathFills ? urlPathFills[path.substring(1)] : null;
      url = isNil(value) ? url.replace(pattern, '') : url.replace(pattern, value.toString());
    });
    return url;
  }

  get(params) {
    return this.vmApiManager.getInstance(this.tokenRequired).then(axios => {
      return axios.get(this.prepareUrl(this.url, params), {params});
    });
  }

  post(params) {
    return this.vmApiManager.getInstance(this.tokenRequired).then(axios => {
      return axios.post(this.prepareUrl(this.url, params), params);
    });
  }

  patch(params) {
    return this.vmApiManager.getInstance(this.tokenRequired).then(axios => {
      return axios.patch(this.prepareUrl(this.url, params), params);
    });
  }

  put(params) {
    return this.vmApiManager.getInstance(this.tokenRequired).then(axios => {
      return axios.put(this.prepareUrl(this.url, params), params);
    });
  }

  delete(params) {
    return this.vmApiManager.getInstance(this.tokenRequired).then(axios => {
      return axios.delete(this.prepareUrl(this.url, params), {params});
    });
  }

  getById(id) {
    return this.get({id});
  }
}
