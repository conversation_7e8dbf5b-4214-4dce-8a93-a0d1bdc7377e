// NOTE: add new resources in alphabetical order positions
// NOTE: avoid :id - id's should include the type in the identifier
const resourceEndpoints = {
  aboutMe: '/v2/editor/:editorId/aboutMe', // support post and delete
  aboutMeHeader: '/v2/editor/:editorId/aboutMeHeader', // support post and delete
  accountType: '/v2/editor/:editorId/accountType',
  accountTypeId: '/v2/editor/:editorId/accountType/:accountTypeId',
  activityReportEditor: '/v2/editor/:editorId/activity',
  activityReportPartner: '/v2/partner/:partnerId/activity',
  activityReportProject: '/v2/project/:projectId/activity',
  activityReportUser: '/v2/user/:userId/activity',
  autoBid: '/v2/autoBid/:autoBidId',
  autoBidCohort: '/v2/editor/:editorId/autoBidCohort',
  autoBidCohortId: '/v2/editor/:editorId/autoBidCohort/:autoBidCohortId',
  autoBidConfiguration: '/v2/editor/:editorId/autoBidConfiguration', // get + post
  autoBidConfigurationId: '/v2/autoBidConfiguration/:autoBidConfigurationId', // get + post = update + delete
  autoBidFromAutoBidConfiguration: '/v2/autoBidConfiguration/:autoBidConfigurationId/autoBid',

  badge: '/v2/badge', // get
  badgeCategory: '/v2/badgeCategory', // get
  badgeEditor: '/v2/editor/:editorId/badge', // get
  badgeItemEditor: '/v2/editor/:editorId/badge/:badgeId', // get + delete
  badgeRequest: '/v2/editor/:editorId/badgeRequest', // get + delete

  bidAddon: '/v2/editor/:editorId/bidAddon', // get
  bidAddonId: '/v2/editor/:editorId/bidAddon/:bidAddonId', // get

  categories: '/noauth/v1/category',

  contentSearchPreset: '/v2/content/search/preset',
  contentType: '/v2/content/type/',
  contentTypeCategory: '/v2/content/type/:mediaType/category',
  contentTypeFilter: '/v2/content/type/:mediaType/filter',
  contentTypeSearch: '/v2/content/type/:mediaType/search',
  contentTypeSearchProviderItem: '/v2/content/type/:mediaType/search/:providerId/:itemId',

  creatorFiles: '/v2/creatorFileFolder/:creatorFolderId/creatorFile', // get (batch), create
  creatorFile: '/v2/creatorFile/:creatorFileId', // get, delete, post
  creatorFileBatch: '/v2/creatorFileBatch', // update, delete (batch)
  creatorFileRead: '/v2/creatorFileView', // update (batch)

  creatorFolders: '/v2/creatorFileFolder', // create(single)
  creatorFoldersBatch: '/v2/creatorFileFolderBatch', // delete, update (batch)
  creatorFolder: '/v2/creatorFileFolder/:creatorFolderId', // get, delete, update
  creatorSubFolders: '/v2/creatorFileFolder/:creatorFolderId/childFolder', // get
  creatorPathToFolder: '/v2/creatorFileFolder/:creatorFolderId/path', // get

  dataReportIterationMedia: '/v2/dataReport/:artifactId/iterationMedia', // get

  editor: '/v2/editor/:editorId', // get + post = update
  editorAvailability: '/v2/editorAvailability/:editorId', // rearrange to use /editor/:editorId instead
  editorAvailabilityRearranged: '/v2/editor/:editorId/availability', // get + post
  editorAvailabilityVacationPeriod: '/v2/editorAvailability/:editorId/editorVacationPeriod', // rearrange to use /editor/:editorId instead
  editorAvailabilityVacationRearrangedByEditor: '/v2/editor/:editorId/vacationPeriod', // get
  editorBidAddon: '/v2/autoBidConfiguration/:autoBidConfigurationId/editorBidAddon', // get
  editorBidAddonId: '/v2/editorBidAddon/:editorBidAddonId', // get + post
  editorNoAuth: '/noauth/v1/editor/:editorId', // get
  editorPayout: '/v2/editor/:editorId/payout', // get
  editorPortfolioMedias: '/v2/editor/:editorId/portfolioMedia', // get + post
  editorPortfolioMedia: '/v2/portfolioMedia/:portfolioMediaId', // get + post + delete
  editorProject: '/v2/editor/:editorId/project', // get
  editorProjectStockAssetNotification: '/v2/editor/project/:projectId/stockAsset/notification', // ?add /:editorId
  editorSignup: '/noauth/v2/editorSignup',
  editorVacationPeriod: '/v2/editorVacationPeriod',
  editorVacationPeriodId: '/v2/editorVacationPeriod/:editorVacationPeriodId',

  finalAssetSummary: '/v2/project/:projectId/finalAssets', // get
  finalAsset: '/v2/project/:projectId/finalAsset', // get + post
  finalAssetId: '/v2/finalAsset/:finalAssetid', // get
  fileTypeSummary: '/v2/project/:projectId/fileTypeSummary', // get

  fileUpload: '/v1/fileUpload', // get + post
  fileUploadId: '/v1/fileUpload/:fileUploadId', // get + post

  iterationAnnotations: '/v2/iteration/:iterationMediaId/annotation',
  iterationAnnotation: '/v2/iteration/:iterationMediaId/annotation/:annotationId',
  iterationAnnotationAttachment: '/v2/iteration/:iterationMediaId/annotation/:annotationId/attachment/:attachmentId',
  iterationMention: '/v2/iteration/:iterationMediaId/mention',
  iterationMedia: '/v2/iterationMedia/:iterationMediaId',
  iterationMediaReview: '/v2/iteration/:iterationMediaId/review',

  mediaThumbnail: '/v2/media/:mediaId/thumbnail',
  messageEditor: '/v2/editor/:editorId/inbox',
  messageUser: '/v2/user/:userId/inbox',
  messageAttachment: '/v2/message/:messageId/attachment/:attachmentId',
  messageAttachments: '/v2/message/:messageId/attachment',
  messageChannel: '/v2/messageChannel/:messageChannelId',
  messageChannelMention: '/v2/messageChannel/:messageChannelId/mention',
  messageChannelMessage: '/v2/message/:messageId',
  messageChannelMessages: '/v2/messageChannel/:messageChannelId/message',

  projectBriefNote: '/v2/project/:projectId/briefNote',
  briefNote: '/v2/briefNote/:briefNoteId',
  briefNoteVersionView: '/v2/briefNoteVersion/:versionId/view',
  briefNoteAttachment: '/v2/briefNote/:briefNoteId/attachment',
  briefNoteAttachmentSingle: '/v2/briefNoteAttachment/:attachmentId',

  briefNoteComment: '/v2/briefNote/:briefNoteId/briefNoteComment',
  briefNoteCommentById: '/v2/briefNoteComment/:briefNoteCommentId',
  briefNoteCommentVersionView: '/v2/briefNoteCommentVersionView',

  outputGroup: '/v2/outputGroup/:outputGroupId',
  outputVideo: '/v2/outputVideo/:outputVideoId',
  outputVideoIterationMedia: '/v2/outputVideo/:outputVideoId/iterationMedia',

  partnerAsset: '/v2/project/:projectId/partnerAsset/:partnerAssetId',
  partnerAssetFolder: '/v2/project/:projectId/partnerAssetFolder/:partnerAssetFolderId',
  partnerAssetFolderAssets: '/v2/project/:projectId/partnerAssetFolder/:partnerAssetFolderId/partnerAsset',
  partnerAssetFolderFolder: '/v2/project/:projectId/partnerAssetFolder/:partnerAssetFolderId/folder',
  partnerProject: '/v2/partner/:partnerId/project',
  passwordResetRequest: '/noauth/v2/passwordResetRequest',
  processingStateSummary: '/v2/project/:projectId/processingStateSummary',
  productQuestions: '/v2/product/:productId/question', // get
  project: '/v2/project/:projectId',
  projectBrief: '/v2/projectBrief/:projectBriefId',
  projectBriefs: '/v2/project/:projectId/projectBrief',
  projectContentTypeDownload: '/v2/project/:projectId/content/type/:mediaType/download',

  projectConcepts: '/v2/project/:projectId/concept', // post, get
  projectConcept: '/v2/concept/:conceptId', // post, get, delete

  projectCreatorFolders: '/v2/project/:projectId/creatorFileProjectFolderTree/',

  projectFinalAsset: '/v2/project/:projectId/projectFinalAsset/:projectFinalAssetId',
  projectMedia: '/v2/project/:projectId/media/:projectMediaId',
  projectMediaBundle: '/v2/project/:projectId/mediaBundle/:mediaBundleId',
  projectMediaBundleInfo: '/v2/project/:projectId/mediaBundleInfo',
  projectMediaFolderId: '/v2/folder/:folderId',
  projectMediaFolderRoot: '/v2/project/:projectId/folder',
  projectMediaFolders: '/v2/folder/:folderId/list',
  projectMilestone: '/v2/project/:projectId/milestone',
  projectMilestoneRole: '/v2/project/:projectId/milestoneRole',
  projectMilestoneRoleCategory: '/v2/project/:projectId/milestoneRole/:roleCategory',
  projectNotificationService: '/v2/notificationPreference/:notificationPreferenceId',
  projectNotificationServices: '/v2/project/:projectId/notificationPreference/:notificationPreferenceId',
  projectOutputGroup: '/v2/project/:projectId/outputGroup',
  projectOutputVideo: '/v2/project/:projectId/outputVideo',
  projectPermission: '/v2/project/:projectId/permission',
  projectQuestion: '/v2/project/:projectId/questionAnswer/:questionId', // get + post
  projectQuestionAttachment: '/v2/questionAttachment/:answerAttachmentId',
  projectQuestionAttachments: '/v2/project/:projectId/questionAttachment/:questionId',
  projectQuestions: '/v2/project/:projectId/questionAnswer',
  projectSow: '/v2/project/:projectId/projectSow',
  projectStockAsset: '/v2/project/:projectId/stockAsset/:stockAssetId',
  projectStockAssetNotification: '/v2/editor/project/:projectId/stockAsset/notification',
  projectStockAssets: '/v2/project/:projectId/stockAsset',
  projectTeam: '/v2/project/:projectId/team',

  projectVidscripts: '/v2/project/:projectId/vidscript', // post, get
  projectVidscript: '/v2/vidscript/:vidscriptId', // post, get, delete

  rushNotification: '/noauth/v2/rushNotification/:rushNotificationId',
  rushNotificationInfo: '/noauth/v2/rushNotification',
  rushNotificationFormat: '/noauth/v2/rushNotification/:rushNotificationId/format',

  resetPassword: '/noauth/v1/resetPassword',

  stockAsset: '/v2/stockAsset/:stockAssetId',
  survey: '/v2/project/:projectId/survey', // + get post

  sowDocument: '/v2/projectSow/:projectSowId',

  user: '/v1/user/:userId',

  userMedia: '/v1/user/media',
  userProject: '/v2/user/project', // ?add /:userId

  vidmobNews: '/v2/vidmobNews',

  // do not use this, we are flattening out this stucture
  v1: {
    userMedia: '/v1/user/media'
  }
};

export default resourceEndpoints;
