import axios from 'axios';
import isNil from 'lodash.isnil';
import isFunction from 'lodash.isfunction';
import Cookies from 'js-cookie';

// by not setting an expiration, cookie is a sessionCookie by default. see: https://github.com/js-cookie/js-cookie#expires

export const StorageTypes = {
  LocalStorage: 'localStorage',
  SessionStorage: 'sessionStorage',
  CookieStorage: 'cookieStorage'
};

const ONE_HOUR_IN_MS = 60 * 60 * 1000;
const DEFAULT_SESSION_IDLE_TIMEOUT_MIN = ONE_HOUR_IN_MS / 60 / 1000;

export const SessionStoreKey = 'vm_session';

function storageAvailable(type) {
  if (type === StorageTypes.CookieStorage) {
    return navigator.cookieEnabled;
  }
  const storage = window[type];
  const x = '__storage_test__';
  try {
    storage.setItem(x, x);
    storage.removeItem(x);
    return true;
  } catch (e) {
    return e instanceof DOMException && (
      // everything except Firefox
      e.code === 22 ||
      // Firefox
      e.code === 1014 ||
      // test name field too, because code might not be present
      // everything except Firefox
      e.name === 'QuotaExceededError' ||
      // Firefox
      e.name === 'NS_ERROR_DOM_QUOTA_REACHED') &&
      // acknowledge QuotaExceededError only if there's something already stored
      storage.length !== 0;
  }
}

export default class VidMobSessionManager {
  sessionInfo;

  constructor(routeToExpectedPage, sessionIdleTimeoutMin = DEFAULT_SESSION_IDLE_TIMEOUT_MIN) {
    this.sessionIdStorageName = SessionStoreKey;
    this.routeToExpectedPage = routeToExpectedPage;
    this.sessionIdleTimeoutMin = sessionIdleTimeoutMin;
    this.storageObj = null;
    this.sessionLastRequestSentTimestamp = null;
    this.sessionInfo = {
      sessionId: null,
      accessToken: null,
      expiresTimestamp: null,
      roles: [],
      store: false
    };
    this.clientToldToLogout = false;
    this.assignStorage();
    this.resetSessionIdleTimeout();
  }

  // attempts all storage options, prioritizing cookies > session > local if they are available
  assignStorage() {
    this.checkAndSetStorage(StorageTypes.LocalStorage);
    this.checkAndSetStorage(StorageTypes.SessionStorage);
    this.checkAndSetStorage(StorageTypes.CookieStorage);
  }

  setSessionTimeoutMin(sessionIdleTimeoutMin) {
    this.sessionIdleTimeoutMin = sessionIdleTimeoutMin;
    this.sessionLastRequestSentTimestamp = Date.now(); // Reset idle timeout when timeout is changed
    this.resetSessionIdleTimeout();
  }

  isSessionExpired() {
    const sessionIdleTimeoutMs = (this.sessionIdleTimeoutMin ? this.sessionIdleTimeoutMin: DEFAULT_SESSION_IDLE_TIMEOUT_MIN) * 60 * 1000; // 1 hour by default
    console.log(`***** session timeout min: ${sessionIdleTimeoutMs / 60000}`);
    return this.sessionLastRequestSentTimestamp && this.sessionLastRequestSentTimestamp <= (Date.now() - sessionIdleTimeoutMs);
  }

  resetSessionIdleTimeout() {
    if (this.isSessionExpired()) {
      this.clearSession();
      console.log('Session timed out');
      return;
    }
    this.sessionLastRequestSentTimestamp = Date.now();
  }

  checkAndSetStorage(storageType) {
    if (!storageAvailable(storageType)) {
      return;
    }

    if (storageType === StorageTypes.CookieStorage) {
      const sessionCookie = Cookies.get(this.sessionIdStorageName);
      if (!sessionCookie) {
        return;
      }
      this.sessionInfo = JSON.parse(sessionCookie);
      this.setStorage(storageType);
      return;
    }

    const sessionItem = window[storageType].getItem(this.sessionIdStorageName);
    if (!sessionItem) {
      return;
    }
    this.sessionInfo = JSON.parse(sessionItem);
    this.setStorage(storageType);
  }

  setStorage(type) {
    if (!storageAvailable(type)) {
      return;
    }

    if (type === StorageTypes.CookieStorage) {
      this.storageObj = {
        removeItem: Cookies.remove,
        setItem: (key, value) => {
          Cookies.set(key, value, {
            sameSite: 'none',
            secure: true
          });
        }
      };
      return;
    }

    this.storageObj = window[type];
  }

  getToken() {
    return this.sessionInfo.accessToken;
  }

  clearSession() {
    this.sessionLastRequestSentTimestamp = null;
    if (this.storageObj !== null) {
      this.storageObj.removeItem(this.sessionIdStorageName);
    }
    this.sessionInfo = {
      sessionId: null,
      accessToken: null,
      expiresTimestamp: null,
      roles: [],
      store: false
    };
  }

  sessionExists() {
    return Boolean(this.sessionInfo) && this.sessionInfo.sessionId !== null;
  }

  triggerUserRedirect() {
    if (this.routeToExpectedPage && isFunction(this.routeToExpectedPage) && !this.clientToldToLogout) {
      this.clientToldToLogout = true;
      this.routeToExpectedPage(this.clientToldToLogout);
    }
  }


  logoutPromise() {
    return new Promise((resolve, reject) => {
      axios
        .post('/api/logout', {
            sessionId: this.sessionInfo.sessionId
        },
        {
          timeout: 10000,
        })
        .then((response) => {
          try {
            this.clearSession();
          } catch (error) {
            console.log('logout error' + error);
          }
          resolve(response.data);
        })
        .catch((error) => {
          console.error('Error:', error);
          reject(error);
        });
    });
  }


  logout() {
    return axios({
      method: 'post',
      url: '/api/logout',
      timeout: 30000,
      data: {
        sessionId: this.sessionInfo.sessionId
      }
    }).then(response => {
      console.log('logout sucessfully');
      return response;
    })
    .catch((err) => {
      console.log('logout error');
      console.log(err);
      throw err;
    })
    .finally(() => {
      console.log('logout finally end');
      this.clearSession();
    });
  }

  login(userCredentials) {
    this.clientToldToLogout = false;
    const userResponse = {};
    userResponse.store = userCredentials ? userCredentials.store : undefined;
    if (userResponse.store) {
      this.setStorage(userResponse.store);
    } else {
      this.setStorage(StorageTypes.SessionStorage);
    }
    return axios({
      method: 'post',
      url: '/api/login',
      data: {
        username: userCredentials.username,
        password: userCredentials.password,
        twoFactorAuthPassword: userCredentials.twoFactorAuthPassword
      }
    }).then(response => {
      userResponse.sessionId = response.data.result.sessionId;
      userResponse.accessToken = response.data.result.accessToken;
      userResponse.expiresIn = response.data.result.expiresIn;
      userResponse.roles = response.data.result.roles;
      this.updateSessionInfo(userResponse);
      return response;
    });
  }

  ssoSession(userCredentials) {
    this.clientToldToLogout = false;
    const userResponse = {};
    userResponse.store = userCredentials ? userCredentials.store : undefined;
    if (userResponse.store) {
      this.setStorage(userResponse.store);
    } else {
      this.setStorage(StorageTypes.SessionStorage);
    }

    return axios({
      method: 'post',
      url: '/api/sso/session',
      data: {
        code: userCredentials.code
      }
    }).then(response => {
      userResponse.sessionId = response.data.result.sessionId;
      userResponse.accessToken = response.data.result.accessToken;
      userResponse.expiresIn = response.data.result.expiresIn;
      userResponse.roles = [];
      this.updateSessionInfo(userResponse);
      return response;
    }).catch(error => {
      throw error;
    });
  }

  requestNewToken() {
    return axios({
      method: 'post',
      url: '/api/session',
      data: {
        sessionId: this.sessionInfo.sessionId
      }
    }).then(response => {
      return response;
    }).catch(error => {
      this.triggerUserRedirect();
      throw error;
    });
  }

  /**
   * Use to determine if the user has a valid session.
   *
   * @returns {Promise<Resolves when session valid. Reject when not.>}
   */
  sessionIsValid() {
    this.resetSessionIdleTimeout();

    return new Promise((resolve, reject) => {
      if (this.sessionInfo.sessionId === null) {
        reject(new Error('No valid session'));
      } else {
        const now = (new Date()).getTime();
        if (this.sessionInfo.expiresTimestamp < now) {
          this.requestNewToken().then(response => {
            this.updateSessionInfo(response.data.result);
            resolve(this.sessionInfo);
          })
            .catch(err => {
              reject(err);
            });
        } else {
          resolve(this.sessionInfo);
        }
      }
    });
  }

  /**
   * This function is meant to be used when making API calls and we need to redirect a user by calling the callBack
   * function provided with the instance of this class. Typically this should only be used by the client-session-manager
   * itself.
   *
   * @returns {Promise<Resolves when session valid. Reject when not.>}
   */
  sessionIsValidWithRedirect() {
    this.resetSessionIdleTimeout();

    return new Promise((resolve, reject) => {
      if (this.sessionInfo.sessionId === null) {
        reject(new Error('No valid session'));
        this.triggerUserRedirect();
      } else {
        const now = (new Date()).getTime();
        if (this.sessionInfo.expiresTimestamp < now) {
          this.requestNewToken().then(response => {
            this.updateSessionInfo(response.data.result);
            resolve(this.sessionInfo);
          })
            .catch(err => {
              if (err && err.response) {
                console.error('Error trying to get new session token.', err);
                reject(err);
              } else if (err && err.request) {
                /**
                 * In the case of a network failure during a users check to see if their session is valid
                 * we do not want to just reject the session. If we do the user will be logged out even if they
                 * should not be.
                 */
                console.error('Network error trying to get new session token.', err);
                resolve(this.sessionInfo);
              } else {
                /**
                 * If we can not determine the type of error we are catching we will get here.
                 */
                console.error('Unknown error trying to get new session token.', err);
                reject(err);
              }
            });
        } else {
          resolve(this.sessionInfo);
        }
      }
    });
  }

  updateSessionInfo(response) {
    if (isNil(response)) {
      return;
    }
    const now = (new Date()).getTime();
    const expiresTimestamp = (now + (response.expiresIn * 1000));
    this.sessionInfo.accessToken = response.accessToken;
    this.sessionInfo.sessionId = response.sessionId;
    this.sessionInfo.roles = response.roles;
    this.sessionInfo.expiresTimestamp = expiresTimestamp;
    this.sessionLastRequestSentTimestamp = Date.now();
    if (!isNil(response.store)) {
      this.sessionInfo.store = response.store;
    }
    if (this.storageObj !== null) {
      this.storageObj.setItem(this.sessionIdStorageName, JSON.stringify(this.sessionInfo));
    }
  }
}
