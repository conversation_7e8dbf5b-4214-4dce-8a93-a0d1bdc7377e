import VidMobSessionManager, {SessionStoreKey, StorageTypes} from './session';
import axios from 'axios';
import Cookies from 'js-cookie';

const testExpiresIn = 60;
const testSession = {
  sessionId: 'testSessionId',
  accessToken: 'testAccessToken',
  expiresTimestamp: 1, // set in beforeAll
  roles: []
};

const testLoginResponse = {
  accessToken: testSession.accessToken,
  sessionId: testSession.sessionId,
  expiresIn: testExpiresIn,
  roles: []
};

jest.mock('axios');
axios.mockImplementation(({method, url}) => {
  if (method === 'post' && url.includes('/api/login')) {
    return Promise.resolve({data: {result: testLoginResponse}});
  }
  if (method === 'post' && url.includes('/api/logout')) {
    return Promise.resolve();
  }

  return jest.requireActual('axios');
});

describe('VidMobSessionManager', () => {
  beforeAll(() => {
    // mock date so .login() can be deterministically tested
    const mockNow = new Date(2023, 1, 1);
    jest.useFakeTimers('modern');
    jest.setSystemTime(mockNow);
    testSession.expiresTimestamp = mockNow.getTime() + (testExpiresIn * 1000);
  });

  afterAll(() => {
    jest.useRealTimers();
  });
  afterEach(() => {
    window.localStorage.clear();
    window.sessionStorage.clear();
    document.cookie = '';
  });

  it('on initialization, prioritizes cookies > session > local storage when present', async () => {
    // only localStorage is set, so use that
    window.localStorage.setItem(SessionStoreKey, JSON.stringify(testSession));
    let manager = new VidMobSessionManager();
    expect(manager.storageObj).toBe(window.localStorage);
    expect(manager.sessionInfo).toStrictEqual(testSession);
    expect(manager.getToken()).toBe(testSession.accessToken);

    // when both localStorage and sessionStorage are set, use sessionStorage
    window.sessionStorage.setItem(SessionStoreKey, JSON.stringify(testSession));
    manager = new VidMobSessionManager();
    expect(manager.storageObj).toBe(window.sessionStorage);
    expect(manager.sessionInfo).toStrictEqual(testSession);
    expect(manager.getToken()).toBe(testSession.accessToken);

    // if all three are set, use cookies
    document.cookie = `${SessionStoreKey}=${encodeURIComponent(JSON.stringify(testSession))}`;
    manager = new VidMobSessionManager();
    expect(manager.storageObj).not.toBe(window.localStorage);
    expect(manager.storageObj).not.toBe(window.sessionStorage);
    expect(manager.storageObj).toHaveProperty('removeItem');
    expect(manager.storageObj).toHaveProperty('setItem');
    expect(manager.sessionInfo).toStrictEqual(testSession);
    expect(manager.getToken()).toBe(testSession.accessToken);
  });

  it('login method uses correct storage', async () => {
    const manager = new VidMobSessionManager();

    // default is session storage, and store is not set in token
    await manager.login({});
    expect(manager.storageObj).toBe(window.sessionStorage);
    expect(manager.sessionInfo).toStrictEqual(testSession);
    expect(manager.getToken()).toBe(testSession.accessToken);
    expect(JSON.parse(window.sessionStorage.getItem(SessionStoreKey))).toStrictEqual(testSession);

    // specifying session storage does the same thing but store IS set in token
    let expectedTestSession = {...testSession, store: StorageTypes.SessionStorage};
    await manager.login({store: StorageTypes.SessionStorage});
    expect(manager.storageObj).toBe(window.sessionStorage);
    expect(manager.sessionInfo).toStrictEqual(expectedTestSession);
    expect(manager.getToken()).toBe(testSession.accessToken);
    expect(JSON.parse(window.sessionStorage.getItem(SessionStoreKey))).toStrictEqual(expectedTestSession);

    // specifying local storage will use local storage
    expectedTestSession = {...testSession, store: StorageTypes.LocalStorage};
    await manager.login({store: StorageTypes.LocalStorage});
    expect(manager.storageObj).toBe(window.localStorage);
    expect(manager.sessionInfo).toStrictEqual(expectedTestSession);
    expect(manager.getToken()).toBe(testSession.accessToken);
    expect(JSON.parse(window.localStorage.getItem(SessionStoreKey))).toStrictEqual(expectedTestSession);

    // specifying local storage will use local storage
    expectedTestSession = {...testSession, store: StorageTypes.CookieStorage};
    await manager.login({store: StorageTypes.CookieStorage});
    expect(manager.storageObj).not.toBe(window.localStorage);
    expect(manager.storageObj).not.toBe(window.sessionStorage);
    expect(manager.storageObj).toHaveProperty('removeItem');
    expect(manager.storageObj).toHaveProperty('setItem');
    expect(manager.sessionInfo).toStrictEqual(expectedTestSession);
    expect(manager.getToken()).toBe(testSession.accessToken);
    expect(JSON.parse(decodeURIComponent(document.cookie.split('=')[1]))).toStrictEqual(expectedTestSession);
  });

  it('clear session if idle timeout is reached', async () => {
    const manager = new VidMobSessionManager();
    const sessionLoggedIn = {...testSession, store: StorageTypes.CookieStorage};
    const sessionEmpty =  {
      sessionId: null,
      accessToken: null,
      expiresTimestamp: null,
      roles: [],
      store: false
    };

    await manager.login({store: StorageTypes.CookieStorage});
    expect(manager.sessionInfo).toStrictEqual(sessionLoggedIn);
    expect(manager.sessionTimeoutId).not.toBeNull();

    jest.runAllTimers();
    expect(manager.sessionInfo).toStrictEqual(sessionEmpty);
  });
});
