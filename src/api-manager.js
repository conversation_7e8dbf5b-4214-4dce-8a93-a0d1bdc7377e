import axios from 'axios';

import VidMobAuthApi from './api';
import resourceEndpoints from './resources';

export default class VidMobApiManager {
  constructor(config, vmSessionManager) {
    this.sessionManager = vmSessionManager;
    this.baseApiUrl = config.apiUrl;
    this.apiVersion = config.apiVersion;
    this.lastTokenStored = null;
    this.axiosInstance = axios.create({
      baseURL: this.baseApiUrl,
      headers: {'VidMob-App-Version': this.apiVersion}
    });
  }

  setAuthHeader(token) {
    if (this.lastTokenStored === null || this.lastTokenStored !== token) {
      this.lastTokenStored = token;
      this.axiosInstance.defaults.headers.common.Authorization = 'Bearer ' + token;
    }
  }

  resourceList() {
    return Object.keys(resourceEndpoints).sort();
  }

  resource(endPoint) {
    const authNeeded = !(endPoint.includes('/noauth/'));
    return new VidMobAuth<PERSON>pi(this, '/api' + endPoint, authNeeded);
  }

  // Used to return an empty promise that resolves.
  canceledRequestPromise() {
    return new Promise(resolve => {
      resolve({});
    });
  }

  getInstance(tokenRequired) {
    return new Promise(resolve => {
      if (tokenRequired) {
        this.sessionManager.sessionIsValidWithRedirect().then(response => {
          this.setAuthHeader(response.accessToken);
          resolve(this.axiosInstance);
        }).catch(() => {
          // Because we want the user to be redirected to the login page we cancel the API request being made
          // and return an empty promise. If we reject the call it results in the client showing an API error and
          // not the session expired message.
          this.canceledRequestPromise();
        });
      } else {
        resolve(this.axiosInstance);
      }
    });
  }
}
